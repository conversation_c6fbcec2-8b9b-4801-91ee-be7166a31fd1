import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/fitbit_models.dart';
import '../services/fitbit_service.dart';

/// Provider for FitBit service instance
final fitbitServiceProvider = Provider<FitBitService>((ref) {
  return FitBitService();
});

/// Provider for FitBit connection status
final fitbitConnectionStatusProvider = StateNotifierProvider<FitBitConnectionStatusNotifier, FitBitConnectionStatus>((ref) {
  return FitBitConnectionStatusNotifier(ref.read(fitbitServiceProvider));
});

/// Provider for FitBit user data
final fitbitUserProvider = StateNotifierProvider<FitBitUserNotifier, FitBitUser?>((ref) {
  return FitBitUserNotifier(ref.read(fitbitServiceProvider));
});

/// Provider for FitBit daily data
final fitbitDayDataProvider = StateNotifierProvider<FitBitDayDataNotifier, FitBitDayData?>((ref) {
  return FitBitDayDataNotifier(ref.read(fitbitServiceProvider));
});

/// Provider for FitBit loading state
final fitbitLoadingProvider = StateNotifierProvider<FitBitLoadingNotifier, bool>((ref) {
  return FitBitLoadingNotifier();
});

/// Provider for FitBit error state
final fitbitErrorProvider = StateNotifierProvider<FitBitErrorNotifier, String?>((ref) {
  return FitBitErrorNotifier();
});

/// Notifier for FitBit connection status
class FitBitConnectionStatusNotifier extends StateNotifier<FitBitConnectionStatus> {
  final FitBitService _fitbitService;

  FitBitConnectionStatusNotifier(this._fitbitService) : super(FitBitConnectionStatus.disconnected) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _fitbitService.initialize();
      state = _fitbitService.connectionStatus;
    } catch (e) {
      log('Error initializing FitBit connection status: $e');
      state = FitBitConnectionStatus.error;
    }
  }

  /// Start authentication process
  Future<bool> authenticate() async {
    try {
      state = FitBitConnectionStatus.connecting;
      final success = await _fitbitService.authenticate();
      
      if (!success) {
        state = FitBitConnectionStatus.error;
      }
      
      return success;
    } catch (e) {
      log('Error during FitBit authentication: $e');
      state = FitBitConnectionStatus.error;
      return false;
    }
  }

  /// Handle OAuth callback
  Future<bool> handleAuthCallback(String authorizationCode, String codeVerifier) async {
    try {
      state = FitBitConnectionStatus.connecting;
      final success = await _fitbitService.handleAuthCallback(authorizationCode, codeVerifier);
      
      if (success) {
        state = FitBitConnectionStatus.connected;
      } else {
        state = FitBitConnectionStatus.error;
      }
      
      return success;
    } catch (e) {
      log('Error handling FitBit auth callback: $e');
      state = FitBitConnectionStatus.error;
      return false;
    }
  }

  /// Refresh tokens
  Future<bool> refreshTokens() async {
    try {
      final success = await _fitbitService.refreshTokens();
      state = _fitbitService.connectionStatus;
      return success;
    } catch (e) {
      log('Error refreshing FitBit tokens: $e');
      state = FitBitConnectionStatus.error;
      return false;
    }
  }

  /// Disconnect from FitBit
  Future<void> disconnect() async {
    try {
      await _fitbitService.disconnect();
      state = FitBitConnectionStatus.disconnected;
    } catch (e) {
      log('Error disconnecting from FitBit: $e');
      state = FitBitConnectionStatus.error;
    }
  }

  /// Update connection status
  void updateStatus(FitBitConnectionStatus status) {
    state = status;
  }
}

/// Notifier for FitBit user data
class FitBitUserNotifier extends StateNotifier<FitBitUser?> {
  final FitBitService _fitbitService;

  FitBitUserNotifier(this._fitbitService) : super(null) {
    _loadUser();
  }

  Future<void> _loadUser() async {
    try {
      state = _fitbitService.currentUser;
    } catch (e) {
      log('Error loading FitBit user: $e');
    }
  }

  /// Fetch user data from FitBit API
  Future<void> fetchUser() async {
    try {
      final user = await _fitbitService.fetchUser();
      state = user;
    } catch (e) {
      log('Error fetching FitBit user: $e');
    }
  }

  /// Clear user data
  void clearUser() {
    state = null;
  }
}

/// Notifier for FitBit daily data
class FitBitDayDataNotifier extends StateNotifier<FitBitDayData?> {
  final FitBitService _fitbitService;

  FitBitDayDataNotifier(this._fitbitService) : super(null);

  /// Fetch data for a specific date
  Future<void> fetchDayData(DateTime date) async {
    try {
      final dayData = await _fitbitService.fetchDayData(date);
      state = dayData;
    } catch (e) {
      log('Error fetching FitBit day data: $e');
    }
  }

  /// Fetch today's data
  Future<void> fetchTodayData() async {
    await fetchDayData(DateTime.now());
  }

  /// Clear day data
  void clearDayData() {
    state = null;
  }
}

/// Notifier for FitBit loading state
class FitBitLoadingNotifier extends StateNotifier<bool> {
  FitBitLoadingNotifier() : super(false);

  void setLoading(bool loading) {
    state = loading;
  }
}

/// Notifier for FitBit error state
class FitBitErrorNotifier extends StateNotifier<String?> {
  FitBitErrorNotifier() : super(null);

  void setError(String? error) {
    state = error;
  }

  void clearError() {
    state = null;
  }
}

/// Combined FitBit provider that manages all FitBit-related state
class FitBitProvider extends StateNotifier<FitBitState> {
  final FitBitService _fitbitService;
  // ignore: unused_field
  final Ref _ref;

  FitBitProvider(this._fitbitService, this._ref) : super(FitBitState.initial()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true);
      
      await _fitbitService.initialize();
      
      state = state.copyWith(
        connectionStatus: _fitbitService.connectionStatus,
        user: _fitbitService.currentUser,
        isLoading: false,
      );

      // If connected, fetch today's data
      if (_fitbitService.isAuthenticated) {
        await fetchTodayData();
      }
    } catch (e) {
      log('Error initializing FitBit provider: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize FitBit: $e',
      );
    }
  }

  /// Start authentication
  Future<bool> authenticate() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final success = await _fitbitService.authenticate();
      
      state = state.copyWith(
        isLoading: false,
        connectionStatus: success ? FitBitConnectionStatus.connecting : FitBitConnectionStatus.error,
        error: success ? null : 'Failed to start authentication',
      );
      
      return success;
    } catch (e) {
      log('Error during FitBit authentication: $e');
      state = state.copyWith(
        isLoading: false,
        connectionStatus: FitBitConnectionStatus.error,
        error: 'Authentication error: $e',
      );
      return false;
    }
  }

  /// Handle OAuth callback
  Future<bool> handleAuthCallback(String authorizationCode, String codeVerifier) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final success = await _fitbitService.handleAuthCallback(authorizationCode, codeVerifier);
      
      if (success) {
        final user = await _fitbitService.fetchUser();
        await fetchTodayData();
        
        state = state.copyWith(
          isLoading: false,
          connectionStatus: FitBitConnectionStatus.connected,
          user: user,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          connectionStatus: FitBitConnectionStatus.error,
          error: 'Failed to complete authentication',
        );
      }
      
      return success;
    } catch (e) {
      log('Error handling FitBit auth callback: $e');
      state = state.copyWith(
        isLoading: false,
        connectionStatus: FitBitConnectionStatus.error,
        error: 'Callback error: $e',
      );
      return false;
    }
  }

  /// Fetch today's data
  Future<void> fetchTodayData() async {
    if (!_fitbitService.isAuthenticated) return;

    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final dayData = await _fitbitService.fetchDayData(DateTime.now());
      
      state = state.copyWith(
        isLoading: false,
        todayData: dayData,
      );
    } catch (e) {
      log('Error fetching today\'s FitBit data: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to fetch data: $e',
      );
    }
  }

  /// Disconnect from FitBit
  Future<void> disconnect() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _fitbitService.disconnect();
      
      state = FitBitState.initial();
    } catch (e) {
      log('Error disconnecting from FitBit: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to disconnect: $e',
      );
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// FitBit state model
class FitBitState {
  final FitBitConnectionStatus connectionStatus;
  final FitBitUser? user;
  final FitBitDayData? todayData;
  final bool isLoading;
  final String? error;

  FitBitState({
    required this.connectionStatus,
    this.user,
    this.todayData,
    required this.isLoading,
    this.error,
  });

  factory FitBitState.initial() {
    return FitBitState(
      connectionStatus: FitBitConnectionStatus.disconnected,
      isLoading: false,
    );
  }

  FitBitState copyWith({
    FitBitConnectionStatus? connectionStatus,
    FitBitUser? user,
    FitBitDayData? todayData,
    bool? isLoading,
    String? error,
  }) {
    return FitBitState(
      connectionStatus: connectionStatus ?? this.connectionStatus,
      user: user ?? this.user,
      todayData: todayData ?? this.todayData,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  bool get isConnected => connectionStatus == FitBitConnectionStatus.connected;
  bool get isDisconnected => connectionStatus == FitBitConnectionStatus.disconnected;
  bool get hasError => error != null;
}

/// Main FitBit provider
final fitbitProvider = StateNotifierProvider<FitBitProvider, FitBitState>((ref) {
  return FitBitProvider(ref.read(fitbitServiceProvider), ref);
});
