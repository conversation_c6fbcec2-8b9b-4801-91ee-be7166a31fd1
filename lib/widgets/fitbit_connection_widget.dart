import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/fitbit_models.dart';
import '../providers/fitbit_provider.dart';
import '../screens/fitbit_auth_screen.dart';
import '../constants/app_colors.dart';
import '../common/utils/size.dart';

/// Widget to display FitBit connection status and allow connection management
class FitBitConnectionWidget extends ConsumerWidget {
  final bool showFullDetails;
  final VoidCallback? onConnectionChanged;

  const FitBitConnectionWidget({
    super.key,
    this.showFullDetails = false,
    this.onConnectionChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fitbitState = ref.watch(fitbitProvider);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        border: Border.all(color: AppColors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildStatusIcon(fitbitState.connectionStatus),
              Space.width(MySize.size12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FitBit Integration',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.text,
                      ),
                    ),
                    Space.height(MySize.size4),
                    Text(
                      _getStatusText(fitbitState),
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: _getStatusColor(fitbitState.connectionStatus),
                      ),
                    ),
                  ],
                ),
              ),
              if (fitbitState.isLoading)
                SizedBox(
                  width: MySize.size20,
                  height: MySize.size20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                )
              else
                _buildActionButton(context, fitbitState),
            ],
          ),

          if (showFullDetails && fitbitState.isConnected) ...[
            Space.height(MySize.size16),
            _buildConnectionDetails(fitbitState),
          ],

          if (fitbitState.hasError) ...[
            Space.height(MySize.size12),
            _buildErrorMessage(ref, fitbitState.error!),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusIcon(FitBitConnectionStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case FitBitConnectionStatus.connected:
        icon = Icons.check_circle;
        color = AppColors.success;
        break;
      case FitBitConnectionStatus.connecting:
        icon = Icons.sync;
        color = AppColors.warning;
        break;
      case FitBitConnectionStatus.tokenExpired:
        icon = Icons.warning;
        color = AppColors.warning;
        break;
      case FitBitConnectionStatus.error:
        icon = Icons.error;
        color = AppColors.error;
        break;
      default:
        icon = Icons.link_off;
        color = AppColors.textSecondary;
    }

    return Container(
      width: MySize.size40,
      height: MySize.size40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MySize.size20),
      ),
      child: Icon(
        icon,
        color: color,
        size: MySize.size20,
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, FitBitState state) {
    String buttonText;
    VoidCallback? onPressed;
    Color buttonColor = AppColors.primary;

    switch (state.connectionStatus) {
      case FitBitConnectionStatus.connected:
        buttonText = 'Manage';
        onPressed = () => _navigateToAuthScreen(context);
        break;
      case FitBitConnectionStatus.tokenExpired:
      case FitBitConnectionStatus.error:
      case FitBitConnectionStatus.disconnected:
      default:
        buttonText = 'Connect';
        onPressed = () => _navigateToAuthScreen(context);
    }

    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: buttonColor,
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size8,
        ),
      ),
      child: Text(
        buttonText,
        style: TextStyle(
          fontSize: MySize.size14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildConnectionDetails(FitBitState state) {
    if (state.user == null && state.todayData == null) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (state.user != null) ...[
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: MySize.size16,
                  color: AppColors.textSecondary,
                ),
                Space.width(MySize.size8),
                Text(
                  state.user!.displayName,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.text,
                  ),
                ),
              ],
            ),
            Space.height(MySize.size8),
          ],

          if (state.todayData?.activity != null) ...[
            Text(
              'Today\'s Activity:',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
            Space.height(MySize.size8),
            Row(
              children: [
                if (state.todayData!.activity!.steps != null) ...[
                  _buildDataPoint(
                    Icons.directions_walk,
                    '${state.todayData!.activity!.steps}',
                    'steps',
                  ),
                  Space.width(MySize.size16),
                ],
                if (state.todayData!.heartRate?.averageHeartRate != null) ...[
                  _buildDataPoint(
                    Icons.favorite,
                    '${state.todayData!.heartRate!.averageHeartRate!.round()}',
                    'bpm',
                  ),
                  Space.width(MySize.size16),
                ],
                if (state.todayData!.activity!.calories != null) ...[
                  _buildDataPoint(
                    Icons.local_fire_department,
                    '${state.todayData!.activity!.calories!.round()}',
                    'cal',
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDataPoint(IconData icon, String value, String unit) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: MySize.size14,
          color: AppColors.primary,
        ),
        Space.width(MySize.size4),
        Text(
          value,
          style: TextStyle(
            fontSize: MySize.size12,
            fontWeight: FontWeight.w600,
            color: AppColors.text,
          ),
        ),
        Space.width(MySize.size2),
        Text(
          unit,
          style: TextStyle(
            fontSize: MySize.size10,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorMessage(WidgetRef ref, String error) {
    return Container(
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: MySize.size16,
          ),
          Space.width(MySize.size8),
          Expanded(
            child: Text(
              error,
              style: TextStyle(
                fontSize: MySize.size12,
                color: AppColors.error,
              ),
            ),
          ),
          IconButton(
            onPressed: () => ref.read(fitbitProvider.notifier).clearError(),
            icon: Icon(
              Icons.close,
              color: AppColors.error,
              size: MySize.size16,
            ),
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(
              minWidth: MySize.size24,
              minHeight: MySize.size24,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(FitBitState state) {
    switch (state.connectionStatus) {
      case FitBitConnectionStatus.connected:
        return state.user?.displayName ?? 'Connected';
      case FitBitConnectionStatus.connecting:
        return 'Connecting...';
      case FitBitConnectionStatus.tokenExpired:
        return 'Connection expired';
      case FitBitConnectionStatus.error:
        return 'Connection error';
      default:
        return 'Not connected';
    }
  }

  Color _getStatusColor(FitBitConnectionStatus status) {
    switch (status) {
      case FitBitConnectionStatus.connected:
        return AppColors.success;
      case FitBitConnectionStatus.connecting:
        return AppColors.warning;
      case FitBitConnectionStatus.tokenExpired:
        return AppColors.warning;
      case FitBitConnectionStatus.error:
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  void _navigateToAuthScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FitBitAuthScreen(),
      ),
    ).then((_) {
      // Call callback when returning from auth screen
      onConnectionChanged?.call();
    });
  }
}

/// Compact version of FitBit connection widget for use in cards
class FitBitConnectionCard extends ConsumerWidget {
  const FitBitConnectionCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fitbitState = ref.watch(fitbitProvider);

    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      color: AppColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MySize.size12),
        side: BorderSide(color: AppColors.grey.withOpacity(0.2)),
      ),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FitBitAuthScreen(),
          ),
        ),
        borderRadius: BorderRadius.circular(MySize.size12),
        child: Padding(
          padding: EdgeInsets.all(MySize.size16),
          child: Row(
            children: [
              Container(
                width: MySize.size48,
                height: MySize.size48,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(MySize.size24),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: AppColors.primary,
                  size: MySize.size24,
                ),
              ),
              Space.width(MySize.size16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FitBit',
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.text,
                      ),
                    ),
                    Space.height(MySize.size4),
                    Text(
                      fitbitState.isConnected
                          ? 'Connected • Syncing data'
                          : 'Connect for more insights',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: fitbitState.isConnected
                            ? AppColors.success
                            : AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                fitbitState.isConnected
                    ? Icons.check_circle
                    : Icons.arrow_forward_ios,
                color: fitbitState.isConnected
                    ? AppColors.success
                    : AppColors.textSecondary,
                size: MySize.size20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
